# AiLex Legal Assistant

A modern legal assistant application built with React, TypeScript, and Express.

## Features

- **Smart Contact Forms**: Enhanced UX with real-time email validation and consistent firstName/lastName fields
- **Responsive Design**: Optimized for all devices with modern UI components
- **API Integration**: Serverless functions for contact submissions and newsletter signups

## Development Setup

This project includes automated code quality gates:
- **Pre-commit hooks**: Prettier formatting and TypeScript checking
- **GitHub Actions CI**: Lint, typecheck, build verification, and security audits

## Available Scripts

- `pnpm run dev` - Start development server
- `pnpm run build` - Build for production
- `pnpm run typecheck` - Run TypeScript type checking
- `pnpm run lint` - Run ESLint
- `pnpm run format` - Format code with Prettier
- `pnpm run format:check` - Check code formatting

## CI/CD Pipeline

The project uses GitHub Actions for continuous integration:
- **Automated testing** on every PR to develop
- **Bundle size monitoring** (400KB limit)
- **Security vulnerability scanning**
- **Code quality gates** with ESLint and TypeScript
